import 'package:flutter/material.dart';
import 'package:pocket_trac/extension.dart';

import '../models/erp_category.dart';
import '../utils/category_icons.dart';
import '../../colors.dart';

class CategoryItem extends StatelessWidget {
  final ErpCategory category;
  final VoidCallback? onTap;

  const CategoryItem({
    required this.category,
    this.onTap,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final categoryName = category.name ?? '未知分類';
    final transactionCount = category.children.length;
    final amount = category.amount ?? 0;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: ErpColors.cardBackground,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        shadowColor: ErpColors.shadow,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                _buildCategoryIcon(categoryName),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCategoryInfo(
                      categoryName, transactionCount, amount),
                ),
                Icon(
                  Icons.chevron_right,
                  color: ErpColors.textHint,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryIcon(String categoryName) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: category.getColor(),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        CategoryIcons.getIcon(categoryName),
        color: category.getColor(),
        size: 20,
      ),
    );
  }

  Widget _buildCategoryInfo(
      String categoryName, int transactionCount, num amount) {
    final isIncome = amount > 0;
    final amountText = amount == 0
        ? '\$0'
        : '${isIncome ? '+' : '-'}\$${amount.abs().toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')}';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          categoryName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$transactionCount 筆交易 • 本月 $amountText',
          style: const TextStyle(
            fontSize: 12,
            color: ErpColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
