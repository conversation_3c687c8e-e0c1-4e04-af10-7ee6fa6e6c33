import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/utils/category_colors.dart';
import 'package:pocket_trac/app/utils/category_icons.dart';
import 'package:pocket_trac/extension.dart';

import '../../../../colors.dart';
import '../controllers/category_detail_controller.dart';

class CategoryDetailView extends GetView<CategoryDetailController> {
  const CategoryDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    Get.lazyPut<CategoryDetailController>(
      () => CategoryDetailController(
        categoryRepository: Get.find(),
      ),
    );
    return LayoutBuilder(
      builder: (context, constraints) {
        return _buildMain();
      },
    );
  }

  Widget _buildMain() {
    return controller.obx(
      (state) => _buildBody(),
      onLoading: const Center(child: CircularProgressIndicator()),
      onError: (error) => Center(
        child: Text(
          'Error: $error',
          style: const TextStyle(color: ErpColors.error),
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    Iterable<Widget> getChildren() sync* {
      yield _buildNameField();
      yield const SizedBox(height: 24);
      yield _buildColorSection();
      yield const SizedBox(height: 24);
      yield _buildIconSection();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 表单内容区域
        Flexible(
          fit: FlexFit.loose,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: controller.formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: getChildren().toList(growable: false),
              ),
            ),
          ),
        ),
        // 底部操作按钮
        _buildBottomActions(),
      ],
    );
  }

  /// 构建分类名称输入框
  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '類別名稱',
          style: TextStyle(
            color: ErpColors.textPrimary,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          // controller: controller.nameController,
          initialValue: controller.draft.name,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '請輸入類別名稱';
            }
            return null;
          },
          onChanged: (value) {
            controller.draft.name = value.trim();
          },
          decoration: InputDecoration(
            hintText: '輸入類別名稱',
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ErpColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ErpColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ErpColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: ErpColors.error),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
          style: const TextStyle(
            fontSize: 16,
            color: ErpColors.textPrimary,
          ),
        ),
      ],
    );
  }

  /// 构建颜色选择区域
  Widget _buildColorSection() {
    Iterable<Widget> getChildren() sync* {
      yield const Text(
        '選擇顏色',
        style: TextStyle(
          color: ErpColors.textPrimary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildColorGrid());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildColorGrid() {
    final colors = CategoryColors.availableColors;
    final draft = controller.draft;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 8,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: colors.length,
      itemBuilder: (context, index) {
        final color = colors[index];
        final isSelected = draft.getColor() == color;

        return GestureDetector(
          onTap: () {
            draft.setColor(color);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: isSelected ? Colors.white : Colors.transparent,
                width: 3,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withOpacity(0.5),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
          ),
        );
      },
    );
  }

  /// 构建图标选择区域
  Widget _buildIconSection() {
    Iterable<Widget> getChildren() sync* {
      yield const Text(
        '選擇圖示',
        style: TextStyle(
          color: ErpColors.textPrimary,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      );
      yield const SizedBox(height: 12);
      yield Obx(() => _buildIconGrid());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: getChildren().toList(growable: false),
    );
  }

  Widget _buildIconGrid() {
    final icons = CategoryIcons.availableIcons;
    final draft = controller.draft;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 6,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: icons.length,
      itemBuilder: (context, index) {
        final icon = icons.elementAt(index);
        final isSelected = draft.getIcon() == icon;

        return GestureDetector(
          onTap: () {
            draft.setIcon(icon);
            controller.refreshDraft();
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? ErpColors.primary.withOpacity(0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected ? ErpColors.primary : ErpColors.border,
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Icon(
              icon,
              color: isSelected ? ErpColors.primary : ErpColors.textSecondary,
              size: 24,
            ),
          ),
        );
      },
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: ErpColors.border),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 取消按钮
            Expanded(
              child: OutlinedButton(
                onPressed: () => Get.back(),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: const BorderSide(color: ErpColors.border),
                ),
                child: const Text(
                  '取消',
                  style: TextStyle(
                    color: ErpColors.textSecondary,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // 保存按钮
            Expanded(
              child: ElevatedButton(
                // onPressed: _saving,
                onPressed: controller.saveCategory,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ErpColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: controller.saveButtonState.obx(
                  (state) {
                    return Text(
                      'save'.tr,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                  onLoading: const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  onError: (error) => Text(
                    'Error: $error',
                    style: const TextStyle(color: ErpColors.error),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saving() async {
    // 显示加载对话框
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    try {
      await controller.saveCategory();
    } catch (e) {
      Get.snackbar(
        '错误',
        '保存分类失败：$e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ErpColors.error,
        colorText: Colors.white,
      );
    } finally {
      Get.back(); // 关闭加载对话框
    }
  }
}
