import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pocket_trac/app/modules/category_detail/views/category_detail_view.dart';
import 'package:pocket_trac/app/widgets/sheet_wrapper.dart';
import 'package:pocket_trac/extension.dart';
import '../../../models/erp_category.dart';
import '../../../widgets/category_item.dart';
import '../../../../colors.dart';
import '../../category_detail/bindings/category_detail_binding.dart';
import '../controllers/categories_controller.dart';

class CategoriesView extends GetView<CategoriesController> {
  const CategoriesView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ErpColors.backgroundLight,
      body: controller.obx(
        (state) => _buildContent(context, controller.categories),
        onLoading: const Center(
          child: CircularProgressIndicator(),
        ),
        onEmpty: _buildEmptyState(),
        onError: _buildErrorState,
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildErrorState(String? error) {
    Iterable<Widget> getChildren() sync* {
      yield Icon(
        Icons.error_outline,
        size: 64,
        color: ErpColors.error,
      );
      yield const SizedBox(height: 16);
      yield Text(
        '載入失敗',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: ErpColors.textPrimary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Text(
        error ?? 'Unknown error',
        style: TextStyle(
          color: ErpColors.textSecondary,
        ),
        textAlign: TextAlign.center,
      );
      yield const SizedBox(height: 16);
      yield ElevatedButton(
        onPressed: controller.onRefresh,
        child: const Text('重試'),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: getChildren().toList(),
      ),
    );
  }

  Widget _buildContent(BuildContext context, Iterable<ErpCategory> it) {
    return RefreshIndicator(
      onRefresh: () async {
        controller.onRefresh();
      },
      child: ListView.builder(
        itemBuilder: (context, index) {
          final category = controller.categories.elementAt(index);
          return CategoryItem(
            category: category,
            onTap: () => _showSheet(category.id ?? 0),
          );
        },
        itemCount: it.length,
      ),
    );
  }

  Widget _buildEmptyState() {
    Iterable<Widget> getChildren() sync* {
      yield Icon(
        Icons.category_outlined,
        size: 64,
        color: ErpColors.textHint,
      );
      yield const SizedBox(height: 16);
      yield Text(
        '尚無分類',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: ErpColors.textSecondary,
        ),
      );
      yield const SizedBox(height: 8);
      yield Text(
        '點擊右下角的 + 按鈕新增第一個分類',
        style: TextStyle(
          color: ErpColors.textHint,
        ),
        textAlign: TextAlign.center,
      );
    }

    return Container(
      padding: const EdgeInsets.all(32),
      alignment: Alignment.center,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: getChildren().toList(growable: false),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ErpColors.gradientStart,
            ErpColors.gradientEnd,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: ErpColors.gradientStart.withOpacity(0.4),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: FloatingActionButton(
        // onPressed: controller.showAddCategorySheet,
        onPressed: _showSheet,
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: const Icon(
          Icons.add,
          color: ErpColors.textWhite,
          size: 24,
        ),
      ),
    );
  }

  Future<void> _showSheet([int categoryId = 0]) async {
    await SheetWrapper(
      title: '新增分類',
      onInit: () {
        // 初始化时可以执行一些操作
        Get.parameters['id'] = '$categoryId';
        final binding = CategoryDetailBinding();
        binding.dependencies();
      },
      child: CategoryDetailView(),
    ).sheet(
      isDismissible: true,
      enableDrag: true,
      isScrollControlled: true,
      ignoreSafeArea: true,
    );
  }
}
