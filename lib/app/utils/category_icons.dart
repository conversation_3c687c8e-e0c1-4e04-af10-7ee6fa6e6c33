import 'package:flutter/material.dart';

class CategoryIcons {
  // 分类图标映射（基于原型设计）
  static final Map<String, IconData> iconMap = {
    // 金錢相關
    '${Icons.attach_money}': Icons.attach_money,
    '${Icons.money_off}': Icons.money_off,
    '${Icons.account_balance}': Icons.account_balance,
    '${Icons.credit_card}': Icons.credit_card,
    '${Icons.savings}': Icons.savings,

    // 生活相關
    '${Icons.restaurant}': Icons.restaurant,
    '${Icons.shopping_bag}': Icons.shopping_bag,
    '${Icons.home}': Icons.home,
    '${Icons.directions_car}': Icons.directions_car,
    '${Icons.sports_esports}': Icons.sports_esports,
    '${Icons.local_hospital}': Icons.local_hospital,
    '${Icons.school}': Icons.school,
    '${Icons.work}': Icons.work,
    '${Icons.electrical_services}': Icons.electrical_services,

    // 通用圖標
    '${Icons.category}': Icons.category,
    '${Icons.add}': Icons.add,
    '${Icons.remove}': Icons.remove,
    '${Icons.edit}': Icons.edit,
    '${Icons.delete}': Icons.delete,
    '${Icons.settings}': Icons.settings,

    // 可以根據需要繼續添加...
  };

  /// 根据分类名称获取图标
  static IconData getIcon(String? iconName) {
    if (hasIcon(iconName)) {
      return iconMap[iconName]!;
    }
    return availableIcons.first;
  }

  static String getIconName(IconData icon) {
    return iconMap.entries
        .firstWhere((entry) => entry.value == icon, orElse: () => MapEntry('', Icons.category))
        .key;
  }

  static Iterable<IconData> get availableIcons => iconMap.values;

  /// 獲取所有可用圖標名稱
  static Iterable<String> get getAllIconNames => iconMap.keys;

  /// 檢查圖標是否存在
  static bool hasIcon(String? iconName) {
    if (iconName == null || iconName.isEmpty) {
      return false;
    }
    return iconMap.containsKey(iconName);
  }
}
